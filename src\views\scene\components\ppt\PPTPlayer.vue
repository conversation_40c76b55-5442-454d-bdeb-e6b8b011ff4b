<template>
  <div class="ppt-player h-full flex flex-col bg-[#0a0f1c]">
    <!-- PPT标题栏 -->
    <div class="flex items-center justify-between p-4 bg-[#1a2332] border-b border-[#2a3441]">
      <div class="flex items-center">
        <FileTextOutlined class="text-[#3B8EE6] text-lg mr-2" />
        <span class="text-white text-sm font-medium">DCIM平台介绍演示</span>
      </div>
      <div class="flex items-center space-x-2">
        <span class="text-gray-400 text-xs">{{ currentSlide + 1 }} / {{ totalSlides }}</span>
        <a-button size="small" type="text" @click="$emit('close')" class="text-gray-400 hover:text-white">
          <CloseOutlined />
        </a-button>
      </div>
    </div>

    <!-- PPT内容区域 -->
    <div class="flex-1 relative overflow-hidden">
      <!-- PPT文件显示区域 -->
      <div class="w-full h-full flex items-center justify-center bg-white">
        <div v-if="!pptLoaded" class="text-center">
          <a-spin size="large" />
          <div class="mt-4 text-gray-500">正在加载PPT文件...</div>
        </div>
        <div v-else-if="pptError" class="text-center text-red-500">
          <ExclamationCircleOutlined class="text-4xl mb-4" />
          <div>PPT文件加载失败</div>
          <div class="text-sm mt-2">{{ pptError }}</div>
        </div>
        <div v-else class="w-full h-full">
          <!-- 这里将集成PPT显示组件 -->
          <iframe v-if="pptUrl" :src="pptUrl" class="w-full h-full border-0" @load="handlePPTLoad" @error="handlePPTError" />
          <div v-else class="flex items-center justify-center h-full text-gray-500">
            <div class="text-center">
              <FileOutlined class="text-6xl mb-4" />
              <div>PPT文件准备中...</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航覆盖层 -->
      <div class="absolute inset-0 pointer-events-none">
        <!-- 左侧导航区域 -->
        <div
          class="absolute left-0 top-0 w-16 h-full pointer-events-auto cursor-pointer hover:bg-black/10 transition-colors flex items-center justify-center"
          @click="previousSlide"
          v-if="currentSlide > 0"
        >
          <LeftOutlined class="text-white text-2xl opacity-70 hover:opacity-100" />
        </div>

        <!-- 右侧导航区域 -->
        <div
          class="absolute right-0 top-0 w-16 h-full pointer-events-auto cursor-pointer hover:bg-black/10 transition-colors flex items-center justify-center"
          @click="nextSlide"
          v-if="currentSlide < totalSlides - 1"
        >
          <RightOutlined class="text-white text-2xl opacity-70 hover:opacity-100" />
        </div>
      </div>
    </div>

    <!-- 控制栏 -->
    <div class="p-4 bg-[#1a2332] border-t border-[#2a3441]">
      <div class="flex items-center justify-between">
        <!-- 播放控制 -->
        <div class="flex items-center space-x-2">
          <a-button size="small" type="primary" @click="toggleAutoPlay" :icon="isAutoPlaying ? h(PauseOutlined) : h(PlayCircleOutlined)">
            {{ isAutoPlaying ? '暂停' : '自动播放' }}
          </a-button>
          <a-select v-model:value="autoPlayInterval" size="small" style="width: 100px" :disabled="!isAutoPlaying">
            <a-select-option :value="3000">3秒</a-select-option>
            <a-select-option :value="5000">5秒</a-select-option>
            <a-select-option :value="8000">8秒</a-select-option>
            <a-select-option :value="10000">10秒</a-select-option>
          </a-select>
        </div>

        <!-- 页面导航 -->
        <div class="flex items-center space-x-2">
          <a-button size="small" @click="previousSlide" :disabled="currentSlide <= 0">
            <LeftOutlined />
            上一页
          </a-button>
          <a-input-number v-model:value="jumpToSlide" size="small" :min="1" :max="totalSlides" style="width: 80px" @pressEnter="goToSlide" />
          <a-button size="small" @click="nextSlide" :disabled="currentSlide >= totalSlides - 1">
            下一页
            <RightOutlined />
          </a-button>
        </div>

        <!-- 视角绑定 -->
        <div class="flex items-center space-x-2">
          <a-button size="small" @click="showViewBindingModal = true">
            <EyeOutlined />
            视角绑定
          </a-button>
          <a-button size="small" type="primary" ghost @click="bindCurrentView" :disabled="!hasCurrentViewBinding"> 绑定当前视角 </a-button>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="mt-3">
        <a-slider
          v-model:value="currentSlide"
          :min="0"
          :max="totalSlides - 1"
          :step="1"
          @change="goToSlideBySlider"
          :tooltip-formatter="(value) => `第 ${value + 1} 页`"
        />
      </div>
    </div>

    <!-- 视角绑定模态框 -->
    <ViewBindingModal
      v-model:visible="showViewBindingModal"
      :current-slide="currentSlide"
      :total-slides="totalSlides"
      @bind-current="handleBindCurrent"
      @preview-binding="handlePreviewBinding"
      @confirm="handleBindingConfirm"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted, h } from 'vue';
  import {
    CloseOutlined,
    LeftOutlined,
    RightOutlined,
    PlayCircleOutlined,
    PauseOutlined,
    EyeOutlined,
    FileOutlined,
    ExclamationCircleOutlined,
    FileTextOutlined, // 添加 FileTextOutlined
  } from '@ant-design/icons-vue';
  import { useGlobalThreeStore } from '../../store/globalThreeStore';
  import type { PPTViewBinding } from '../../store/globalThreeStore';
  import ViewBindingModal from './ViewBindingModal.vue';
  import { CameraController } from '../../lib/CameraController';

  // Props
  interface Props {
    pptPath: string;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    close: [];
    slideChange: [slideIndex: number];
  }>();

  // Store
  const globalThreeStore = useGlobalThreeStore();

  // 响应式数据
  const currentSlide = ref(0);
  const totalSlides = ref(0);
  const jumpToSlide = ref(1);
  const isAutoPlaying = ref(false);
  const autoPlayInterval = ref(5000);
  const showViewBindingModal = ref(false);
  const pptLoaded = ref(false);
  const pptError = ref('');
  const pptUrl = ref('');

  // 自动播放定时器
  let autoPlayTimer: number | null = null;

  // 计算属性
  const hasCurrentViewBinding = computed(() => {
    return globalThreeStore.getPPTViewBinding(currentSlide.value) !== undefined;
  });

  // 方法
  const initializePPT = async () => {
    try {
      // 使用本地PPT文件路径，通过Google Docs Viewer显示
      // 这是一个更可靠的方案，避免跨域问题
      const fullUrl = `${window.location.origin}/${props.pptPath}`;

      // 尝试多种PPT查看器
      const viewers = [
        `https://docs.google.com/gview?url=${encodeURIComponent(fullUrl)}&embedded=true`,
        `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fullUrl)}`,
      ];

      // 使用第一个查看器
      pptUrl.value = viewers[0];

      // 根据实际PPT文件设置页数
      // 这里可以根据具体的PPT文件调整
      totalSlides.value = 25; // DCIM平台介绍PPT的实际页数
      globalThreeStore.setPPTTotalSlides(totalSlides.value);

      pptLoaded.value = true;
      console.log('PPT初始化成功，总页数:', totalSlides.value);
    } catch (error) {
      console.error('PPT初始化失败:', error);
      pptError.value = '无法加载PPT文件';
    }
  };

  const handlePPTLoad = () => {
    pptLoaded.value = true;
  };

  const handlePPTError = () => {
    pptError.value = 'PPT文件加载失败';
  };

  const previousSlide = () => {
    if (currentSlide.value > 0) {
      goToSlide(currentSlide.value - 1);
    }
  };

  const nextSlide = () => {
    if (currentSlide.value < totalSlides.value - 1) {
      goToSlide(currentSlide.value + 1);
    }
  };

  const goToSlide = (slideIndex?: number) => {
    const targetSlide = slideIndex !== undefined ? slideIndex : jumpToSlide.value - 1;
    if (targetSlide >= 0 && targetSlide < totalSlides.value) {
      currentSlide.value = targetSlide;
      jumpToSlide.value = targetSlide + 1;

      // 更新store状态
      globalThreeStore.setPPTCurrentSlide(targetSlide);

      // 触发视角切换
      triggerViewChange(targetSlide);

      // 触发事件
      emit('slideChange', targetSlide);
    }
  };

  const goToSlideBySlider = (value: number) => {
    goToSlide(value);
  };

  const toggleAutoPlay = () => {
    isAutoPlaying.value = !isAutoPlaying.value;
    globalThreeStore.setPPTPlaying(isAutoPlaying.value);

    if (isAutoPlaying.value) {
      startAutoPlay();
    } else {
      stopAutoPlay();
    }
  };

  const startAutoPlay = () => {
    stopAutoPlay(); // 清除现有定时器
    autoPlayTimer = window.setInterval(() => {
      if (currentSlide.value < totalSlides.value - 1) {
        nextSlide();
      } else {
        // 到达最后一页，停止自动播放
        toggleAutoPlay();
      }
    }, autoPlayInterval.value);
  };

  const stopAutoPlay = () => {
    if (autoPlayTimer) {
      clearInterval(autoPlayTimer);
      autoPlayTimer = null;
    }
  };

  const triggerViewChange = (slideIndex: number) => {
    const binding = globalThreeStore.getPPTViewBinding(slideIndex);
    if (binding) {
      const cameraController = CameraController.getInstance();
      if (cameraController) {
        cameraController.moveToPosition(
          binding.cameraPosition,
          binding.cameraTarget,
          1000 // 1秒过渡时间
        );
      }
    }
  };

  const bindCurrentView = () => {
    const cameraController = CameraController.getInstance();
    if (cameraController) {
      const position = cameraController.getCameraPosition();
      const target = cameraController.currentTarget || { x: 0, y: 0, z: 0 };

      const binding: PPTViewBinding = {
        slideIndex: currentSlide.value,
        cameraPosition: { x: position.x, y: position.y, z: position.z },
        cameraTarget: { x: target.x, y: target.y, z: target.z },
        name: `第${currentSlide.value + 1}页视角`,
      };

      globalThreeStore.addPPTViewBinding(binding);
    }
  };

  const handleViewBinding = (binding: PPTViewBinding) => {
    globalThreeStore.addPPTViewBinding(binding);
  };

  const handleRemoveBinding = (slideIndex: number) => {
    globalThreeStore.removePPTViewBinding(slideIndex);
  };

  // 监听自动播放间隔变化
  watch(autoPlayInterval, () => {
    if (isAutoPlaying.value) {
      startAutoPlay(); // 重新启动定时器
    }
  });

  // 生命周期
  onMounted(() => {
    initializePPT();
    jumpToSlide.value = currentSlide.value + 1;
  });

  onUnmounted(() => {
    stopAutoPlay();
  });
</script>

<style scoped>
  .ppt-player {
    background: linear-gradient(135deg, #0a0f1c 0%, #1a2332 100%);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 142, 230, 0.6);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 142, 230, 0.8);
  }
</style>
