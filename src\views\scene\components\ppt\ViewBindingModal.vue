<template>
  <ModalDialog v-model:visible="visibleValue" title="PPT视角绑定管理" width="800px" height="600px" :show-footer="true" :iconSrc="dashboardTitle">
    <div class="h-full flex flex-col p-[1vw]">
      <!-- 工具栏 -->
      <div class="mb-[1vw] flex items-center justify-between">
        <div class="text-[0.9vw] text-white font-medium">当前幻灯片：{{ currentSlide + 1 }} / {{ totalSlides }}</div>
        <div class="flex gap-[0.8vw]">
          <button
            class="px-[1vw] py-[0.4vw] bg-blue-500/80 hover:bg-blue-500 text-white rounded text-[0.7vw] transition-all"
            @click="bindCurrentView"
          >
            绑定当前视角
          </button>
          <button
            class="px-[1vw] py-[0.4vw] bg-green-500/80 hover:bg-green-500 text-white rounded text-[0.7vw] transition-all"
            @click="previewBinding"
          >
            预览绑定
          </button>
        </div>
      </div>

      <!-- 幻灯片列表 -->
      <div class="flex-1 overflow-auto">
        <div class="grid grid-cols-2 gap-[1vw]">
          <div
            v-for="(slide, index) in slides"
            :key="index"
            class="relative bg-[#1a2332] border border-blue-400/30 rounded p-[0.8vw] cursor-pointer transition-all"
            :class="{ 'border-blue-400': currentSlide === index }"
            @click="selectSlide(index)"
          >
            <!-- 幻灯片预览 -->
            <div class="w-full h-[8vw] bg-[#0C1526] rounded mb-[0.5vw] flex items-center justify-center">
              <span class="text-[0.8vw] text-gray-400">幻灯片 {{ index + 1 }}</span>
            </div>

            <!-- 绑定信息 -->
            <div class="text-[0.7vw]">
              <div class="text-white mb-[0.2vw]">视角绑定：</div>
              <div v-if="slide.viewBinding" class="text-green-400">
                <div
                  >位置: ({{ slide.viewBinding.position.x.toFixed(2) }}, {{ slide.viewBinding.position.y.toFixed(2) }},
                  {{ slide.viewBinding.position.z.toFixed(2) }})</div
                >
                <div
                  >目标: ({{ slide.viewBinding.target.x.toFixed(2) }}, {{ slide.viewBinding.target.y.toFixed(2) }},
                  {{ slide.viewBinding.target.z.toFixed(2) }})</div
                >
              </div>
              <div v-else class="text-gray-500">未绑定</div>
            </div>

            <!-- 操作按钮 -->
            <div class="absolute top-[0.5vw] right-[0.5vw] flex gap-[0.3vw]">
              <button
                v-if="slide.viewBinding"
                class="w-[1.5vw] h-[1.5vw] bg-red-500/80 hover:bg-red-500 text-white rounded text-[0.6vw] flex items-center justify-center transition-all"
                @click.stop="clearBinding(index)"
                title="清除绑定"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-[0.8vw]">
        <button
          class="px-[1vw] h-[1.8vw] flex items-center justify-center rounded bg-gray-500/80 hover:bg-gray-500 text-white transition-all text-[0.7vw]"
          @click="handleCancel"
        >
          取消
        </button>
        <button
          class="px-[1vw] h-[1.8vw] flex items-center justify-center rounded bg-blue-500/80 hover:bg-blue-500 text-white transition-all text-[0.7vw]"
          @click="handleConfirm"
        >
          确认
        </button>
      </div>
    </template>
  </ModalDialog>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';

  interface ViewBinding {
    position: { x: number; y: number; z: number };
    target: { x: number; y: number; z: number };
  }

  interface Slide {
    id: number;
    name: string;
    viewBinding?: ViewBinding;
  }

  const props = defineProps<{
    visible: boolean;
    currentSlide?: number;
    totalSlides?: number;
  }>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    confirm: [bindings: ViewBinding[]];
    'bind-current': [slideIndex: number];
    'preview-binding': [slideIndex: number];
  }>();

  const visibleValue = computed({
    get() {
      return props.visible;
    },
    set(value) {
      emit('update:visible', value);
    },
  });

  const currentSlide = ref(props.currentSlide || 0);
  const totalSlides = ref(props.totalSlides || 4);
  const slides = ref<Slide[]>([]);

  // 初始化幻灯片数据
  onMounted(() => {
    initSlides();
  });

  function initSlides() {
    slides.value = Array.from({ length: totalSlides.value }, (_, index) => ({
      id: index,
      name: `幻灯片 ${index + 1}`,
    }));
  }

  function selectSlide(index: number) {
    currentSlide.value = index;
  }

  function bindCurrentView() {
    emit('bind-current', currentSlide.value);
    // 模拟获取当前视角数据
    const mockViewBinding: ViewBinding = {
      position: { x: Math.random() * 10, y: Math.random() * 10, z: Math.random() * 10 },
      target: { x: Math.random() * 5, y: Math.random() * 5, z: Math.random() * 5 },
    };
    slides.value[currentSlide.value].viewBinding = mockViewBinding;
  }

  function previewBinding() {
    if (slides.value[currentSlide.value].viewBinding) {
      emit('preview-binding', currentSlide.value);
    }
  }

  function clearBinding(index: number) {
    delete slides.value[index].viewBinding;
  }

  function handleCancel() {
    emit('update:visible', false);
  }

  function handleConfirm() {
    const bindings = slides.value.map((slide) => slide.viewBinding).filter(Boolean) as ViewBinding[];
    emit('confirm', bindings);
    emit('update:visible', false);
  }
</script>
